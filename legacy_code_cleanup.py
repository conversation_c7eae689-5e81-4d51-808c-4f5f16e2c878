#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Notion-to-WordPress 插件遗留代码清理脚本
自动化处理PSR-4重构后的遗留代码问题

功能：
1. 更新旧类名引用为新的PSR-4命名空间类名
2. 清理手动require_once语句
3. 更新class_exists()检查
4. 更新注释和文档中的类名引用
5. 更新@package声明
"""

import os
import re
import codecs
import shutil
from datetime import datetime
from typing import Dict, List, Tuple

class LegacyCodeCleanup:
    def __init__(self, plugin_root: str):
        self.plugin_root = plugin_root
        self.backup_dir = os.path.join(plugin_root, f'backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
        
        # 类名映射表 - 旧类名 -> 新命名空间类名
        self.class_mappings = {
            # Core classes
            'Notion_Logger': r'\\NTWP\\Core\\Logger',
            'Notion_Performance_Monitor': r'\\NTWP\\Core\\Performance_Monitor',
            'Notion_Memory_Manager': r'\\NTWP\\Core\\Memory_Manager',
            'Notion_Security': r'\\NTWP\\Core\\Security',
            'Notion_Text_Processor': r'\\NTWP\\Core\\Text_Processor',
            'Notion_HTTP_Client': r'\\NTWP\\Core\\HTTP_Client',
            
            # Services classes
            'Notion_API': r'\\NTWP\\Services\\API',
            'Notion_Content_Converter': r'\\NTWP\\Services\\Content_Converter',
            'Notion_Image_Processor': r'\\NTWP\\Services\\Image_Processor',
            'Notion_Incremental_Detector': r'\\NTWP\\Services\\Incremental_Detector',
            'Notion_Sync_Manager': r'\\NTWP\\Services\\Sync_Manager',
            'Notion_Metadata_Extractor': r'\\NTWP\\Services\\Metadata_Extractor',
            
            # Handlers classes
            'Notion_Import_Coordinator': r'\\NTWP\\Handlers\\Import_Coordinator',
            'Notion_To_WordPress_Integrator': r'\\NTWP\\Handlers\\Integrator',
            'Notion_Webhook': r'\\NTWP\\Handlers\\Webhook',
            
            # Utils classes
            'Notion_To_WordPress_Helper': r'\\NTWP\\Utils\\Helper',
            'Notion_Database_Helper': r'\\NTWP\\Utils\\Database_Helper',
            'Notion_Session_Cache': r'\\NTWP\\Utils\\Session_Cache',
            'Notion_Smart_API_Merger': r'\\NTWP\\Utils\\Smart_API_Merger',
            'Notion_Network_Retry': r'\\NTWP\\Utils\\Network_Retry',
            'Notion_Concurrent_Network_Manager': r'\\NTWP\\Utils\\Concurrent_Network_Manager',
            'Notion_Smart_Cache': r'\\NTWP\\Utils\\Smart_Cache',
            'Notion_Config_Simplifier': r'\\NTWP\\Utils\\Config_Simplifier',
            
            # Framework classes
            'Notion_To_WordPress': r'\\NTWP\\Framework\\Main',
            'Notion_To_WordPress_Loader': r'\\NTWP\\Framework\\Loader',
            'Notion_To_WordPress_i18n': r'\\NTWP\\Framework\\i18n',
            
            # Admin classes
            'Notion_To_WordPress_Admin': r'\\NTWP\\Admin\\Admin',
        }
        
        # 需要处理的文件类型
        self.file_extensions = ['.php']
        
        # 需要排除的目录
        self.exclude_dirs = ['vendor', 'node_modules', '.git', 'backup_*']
        
    def create_backup(self):
        """创建备份"""
        print(f"创建备份到: {self.backup_dir}")
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
            
        # 备份关键文件
        key_files = [
            'includes/handlers/Import_Coordinator.php',
            'includes/services/API.php',
            'admin/class-notion-to-wordpress-admin.php',
            'includes/framework/Main.php'
        ]
        
        for file_path in key_files:
            full_path = os.path.join(self.plugin_root, file_path)
            if os.path.exists(full_path):
                backup_path = os.path.join(self.backup_dir, file_path)
                os.makedirs(os.path.dirname(backup_path), exist_ok=True)
                shutil.copy2(full_path, backup_path)
                print(f"  备份: {file_path}")
    
    def get_php_files(self) -> List[str]:
        """获取所有需要处理的PHP文件"""
        php_files = []
        
        for root, dirs, files in os.walk(self.plugin_root):
            # 排除特定目录
            dirs[:] = [d for d in dirs if not any(
                re.match(pattern.replace('*', '.*'), d) for pattern in self.exclude_dirs
            )]
            
            for file in files:
                if any(file.endswith(ext) for ext in self.file_extensions):
                    php_files.append(os.path.join(root, file))
        
        return php_files
    
    def update_class_references(self, content: str) -> Tuple[str, int]:
        """更新类引用"""
        changes = 0
        
        for old_class, new_class in self.class_mappings.items():
            # 1. 更新 class_exists() 检查
            pattern = rf"class_exists\s*\(\s*['\"]({re.escape(old_class)})['\"]"
            replacement = f"class_exists('{new_class.replace(chr(92), chr(92)+chr(92))}')"
            content, count = re.subn(pattern, replacement, content)
            changes += count
            
            # 2. 更新静态方法调用
            pattern = rf"\b{re.escape(old_class)}::"
            replacement = f"{new_class}::"
            content, count = re.subn(pattern, replacement, content)
            changes += count
            
            # 3. 更新 new 实例化
            pattern = rf"\bnew\s+{re.escape(old_class)}\s*\("
            replacement = f"new {new_class}("
            content, count = re.subn(pattern, replacement, content)
            changes += count
            
            # 4. 更新类型声明中的类名
            pattern = rf"\b{re.escape(old_class)}\s+\$"
            replacement = f"{new_class} $"
            content, count = re.subn(pattern, replacement, content)
            changes += count
            
            # 5. 更新注释中的类名引用
            pattern = rf"@param\s+{re.escape(old_class)}\s+"
            replacement = f"@param {new_class} "
            content, count = re.subn(pattern, replacement, content)
            changes += count
            
            pattern = rf"@return\s+{re.escape(old_class)}\s+"
            replacement = f"@return {new_class} "
            content, count = re.subn(pattern, replacement, content)
            changes += count
        
        return content, changes
    
    def remove_manual_requires(self, content: str) -> Tuple[str, int]:
        """移除手动require_once语句"""
        changes = 0
        
        # 匹配手动require_once语句（但保留WordPress核心文件的require）
        patterns = [
            r"require_once\s+plugin_dir_path\s*\(\s*__FILE__\s*\)\s*\.\s*['\"][^'\"]*class-notion[^'\"]*['\"];?\s*\n?",
            r"require_once\s+plugin_dir_path\s*\(\s*__FILE__\s*\)\s*\.\s*['\"][^'\"]*Notion[^'\"]*\.php['\"];?\s*\n?",
            r"//\s*require_once[^\n]*notion[^\n]*\n",
        ]
        
        for pattern in patterns:
            content, count = re.subn(pattern, '', content, flags=re.IGNORECASE)
            changes += count
        
        return content, changes
    
    def update_package_declarations(self, content: str) -> Tuple[str, int]:
        """更新@package声明"""
        changes = 0
        
        # 更新@package声明
        pattern = r"@package\s+Notion_To_WordPress"
        replacement = "@package NTWP"
        content, count = re.subn(pattern, replacement, content)
        changes += count
        
        return content, changes
    
    def process_file(self, file_path: str) -> Dict[str, int]:
        """处理单个文件"""
        try:
            with codecs.open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            content = original_content
            total_changes = 0
            change_types = {
                'class_references': 0,
                'manual_requires': 0,
                'package_declarations': 0
            }
            
            # 1. 更新类引用
            content, changes = self.update_class_references(content)
            change_types['class_references'] = changes
            total_changes += changes
            
            # 2. 移除手动require语句
            content, changes = self.remove_manual_requires(content)
            change_types['manual_requires'] = changes
            total_changes += changes
            
            # 3. 更新@package声明
            content, changes = self.update_package_declarations(content)
            change_types['package_declarations'] = changes
            total_changes += changes
            
            # 如果有变更，写入文件
            if total_changes > 0:
                with codecs.open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✓ 更新: {os.path.relpath(file_path, self.plugin_root)} ({total_changes} 处变更)")
                for change_type, count in change_types.items():
                    if count > 0:
                        print(f"    {change_type}: {count}")
            
            return change_types
            
        except Exception as e:
            print(f"✗ 错误处理文件 {file_path}: {e}")
            return {'class_references': 0, 'manual_requires': 0, 'package_declarations': 0}
    
    def run(self):
        """运行清理过程"""
        print("=== Notion-to-WordPress 遗留代码清理 ===")
        print(f"插件根目录: {self.plugin_root}")
        
        # 创建备份
        self.create_backup()
        
        # 获取所有PHP文件
        php_files = self.get_php_files()
        print(f"\n找到 {len(php_files)} 个PHP文件需要处理")
        
        # 处理文件
        total_stats = {
            'files_processed': 0,
            'files_changed': 0,
            'class_references': 0,
            'manual_requires': 0,
            'package_declarations': 0
        }
        
        for file_path in php_files:
            changes = self.process_file(file_path)
            total_stats['files_processed'] += 1
            
            if sum(changes.values()) > 0:
                total_stats['files_changed'] += 1
                for key, value in changes.items():
                    total_stats[key] += value
        
        # 输出统计
        print(f"\n=== 清理完成 ===")
        print(f"处理文件: {total_stats['files_processed']}")
        print(f"修改文件: {total_stats['files_changed']}")
        print(f"类引用更新: {total_stats['class_references']}")
        print(f"移除手动require: {total_stats['manual_requires']}")
        print(f"更新@package声明: {total_stats['package_declarations']}")
        print(f"\n备份位置: {self.backup_dir}")

if __name__ == "__main__":
    # 获取插件根目录
    plugin_root = os.path.dirname(os.path.abspath(__file__))
    
    # 运行清理
    cleanup = LegacyCodeCleanup(plugin_root)
    cleanup.run()
