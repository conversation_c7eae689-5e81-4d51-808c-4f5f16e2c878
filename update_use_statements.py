#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import codecs
import re

# 需要处理的文件列表
files_to_update = [
    'includes/utils/Smart_API_Merger.php',
    'includes/services/API.php', 
    'includes/handlers/Webhook.php',
    'includes/handlers/Import_Coordinator.php'
]

for file_path in files_to_update:
    try:
        with codecs.open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 移除 use ... as Notion_... 语句
        content = re.sub(r'use\s+[^;]+\s+as\s+Notion_[^;]+;\s*\n', '', content)
        
        if content != original_content:
            with codecs.open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f'Updated: {file_path}')
    except Exception as e:
        print(f'Error processing {file_path}: {e}')

print('Use statement cleanup completed')